package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.search.client.enums.QueryType;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.Response;
import com.ruijing.search.client.service.SearchService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @description: 订单搜索 API RPC接口 客户端
 * @author: zhongyulei
 * @create: 2021/1/19 18:36
 **/
@ServiceClient
public class OrderSearchRPCServiceClient {
    /**
     * 搜索客户端服务
     */
    @MSharpReference(remoteAppkey = "msharp-search-service",group = "pub")
    private SearchService searchService;

    private static final Logger logger = LoggerFactory.getLogger(OrderSearchRPCServiceClient.class);

    @ServiceLog(description = "搜索订单数据接口", serviceType = ServiceType.RPC_CLIENT)
    public Response search(Request request) {
        request.setQueryType(QueryType.NORMAL_AND_DEBUG);
        Preconditions.isTrue(request.getPageSize() <= 10000, "搜索单次查询总长度必须小于10000条");
        Response response = searchService.search(request);
        if (response.isSuccess()) {
            return response;
        }
        // 重试，如果仍不成功，则打印搜索语句，更容易发现问题
        request.setQueryType(QueryType.NORMAL_AND_DEBUG);
        response = searchService.search(request);
        if (response != null && response.isSuccess()) {
            return response;
        }
        String dsl = null;
        if (response != null) {
            dsl = response.getAnalysisResult() == null ? null : response.getAnalysisResult().getDsl();
        }
        logger.error("搜索订单数据异常" + JsonUtils.toJsonIgnoreNull(response) + "，\n搜索dsl：" + dsl + "，\n搜索入参：" + JsonUtils.toJsonIgnoreNull(request));
        Preconditions.isTrue(response.isSuccess(), "搜索订单数据异常" + JsonUtils.toJsonIgnoreNull(response) + "，搜索dsl：" + dsl);
        return response;
    }
}
